🔔 **Notification Settings**

Configure your notification preferences for trading activities\.

**Notification Types:**
• **Trade Confirmations** \- Get notified when trades complete
• **Balance Updates** \- Receive balance change notifications
• **System Alerts** \- Important system messages
• **Error Notifications** \- Failed transaction alerts

**Current Settings:**
• All notifications are currently enabled
• Notifications are sent via Telegram messages
• Real\-time updates for important events

**Notification Features:**
• Instant trade confirmations
• Balance change alerts
• System maintenance notices
• Security alerts
• Error and failure notifications

**Note:** 
Notification preferences will be customizable in future updates\. Currently, all important notifications are enabled by default for security and transparency\.

Use the buttons below to navigate back:
