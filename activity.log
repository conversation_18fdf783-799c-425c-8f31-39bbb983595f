[2025-06-20 06:51:15:919] [ERROR] Failed to get user by ID: PostgresError: Tenant or user not found
[2025-06-20 06:51:36:445] [ERROR] Failed to update keyboard, attempting deletion: GrammyError: Call to 'editMessageText' failed! (400: Bad Request: can't parse entities: Character '.' is reserved and must be escaped with the preceding '\')
[2025-06-20 06:52:14:327] [ERROR] Failed to update keyboard, attempting deletion: GrammyError: Call to 'editMessageText' failed! (400: Bad Request: can't parse entities: Character '.' is reserved and must be escaped with the preceding '\')
[2025-06-20 07:28:52:620] [ERROR] {
  "error": {
    "code": "ENOENT",
    "path": "../content/settings_menu.md",
    "syscall": "open",
    "errno": -2
  },
  "ctx": {
    "update": {
      "update_id": 796425455,
      "message": {
        "message_id": 45,
        "from": {
          "id": 956478384,
          "is_bot": false,
          "first_name": "rikudo",
          "username": "rikudoseninn",
          "language_code": "id"
        },
        "chat": {
          "id": 956478384,
          "first_name": "rikudo",
          "username": "rikudoseninn",
          "type": "private"
        },
        "date": 1750379333,
        "text": "/test",
        "entities": [
          {
            "offset": 0,
            "length": 5,
            "type": "bot_command"
          }
        ]
      }
    },
    "api": {
      "token": "7552314214:AAHEf0p8MrTiU6LZg9J-YJqwEIsTYS0ZMHk",
      "raw": {},
      "config": {}
    },
    "me": {
      "id": 7552314214,
      "is_bot": true,
      "first_name": "GramStax - Bot",
      "username": "gramstax_bot",
      "can_join_groups": true,
      "can_read_all_group_messages": false,
      "supports_inline_queries": false,
      "can_connect_to_business": false,
      "has_main_web_app": false
    },
    "match": ""
  },
  "name": "BotError"
}
