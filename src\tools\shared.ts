import { getSolanaChainType, generateMockSolanaAddress, formatSolanaChainName, getSolanaCurrencySymbol, getSolanaChainId, isSolanaChain } from "./solana"
import { getEthereumChainType, generateMockEthereumAddress, formatEthereumChainName, getEthereumCurrencySymbol, getEthereumChainId, isEthereumChain } from "./ethereum"
import type { TypeChainName } from "../data"

/**
 * Shared blockchain utilities
 *
 * This module provides generic functions that work across all blockchain networks
 * by delegating to the appropriate blockchain-specific modules.
 */

// ============================================================================
// CORE BLOCKCHAIN UTILITIES - Used functions only
// ============================================================================

/**
 * Get chain type from string input with support for various aliases
 * @param chainInput Chain input string (case-insensitive)
 * @returns TypeChainName or null if invalid
 *
 * @example
 * ```typescript
 * getChainType("solana") // Returns staticDataChainList.SOLANA_MAINNET
 * getChainType("eth") // Returns staticDataChainList.ETHEREUM_MAINNET
 * getChainType("invalid") // Returns null
 * ```
 */
export const getChainType = (chainInput: string): TypeChainName | null => {
  // Try each blockchain-specific parser
  return getSolanaChainType(chainInput) || getEthereumChainType(chainInput) || null
}

/**
 * Get the official chain ID for a given chain type
 * @param chain Chain type
 * @returns Chain ID string as used by the blockchain network
 *
 * @example
 * ```typescript
 * getChainId(staticDataChainList.ETHEREUM_MAINNET) // Returns "1"
 * getChainId(staticDataChainList.SOLANA_MAINNET) // Returns "101"
 * ```
 */
export const getChainId = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return getSolanaChainId(chain)
  }
  if (isEthereumChain(chain)) {
    return getEthereumChainId(chain)
  }
  return "0"
}

/**
 * Generate a mock address for the specified blockchain network
 * @param chain Chain type to generate address for
 * @returns Mock address string in the appropriate format for the chain
 *
 * @example
 * ```typescript
 * generateMockAddress(staticDataChainList.SOLANA_MAINNET) // Returns Base58 address
 * generateMockAddress(staticDataChainList.ETHEREUM_MAINNET) // Returns hex address
 * ```
 */
export const generateMockAddress = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return generateMockSolanaAddress(chain)
  }
  if (isEthereumChain(chain)) {
    return generateMockEthereumAddress(chain)
  }
  throw new Error(`Unsupported chain type: ${chain}`)
}

/**
 * Format chain name for user-friendly display
 * @param chain Chain type to format
 * @returns Formatted, human-readable chain name
 *
 * @example
 * ```typescript
 * formatChainName(staticDataChainList.SOLANA_MAINNET) // Returns "Solana Mainnet"
 * formatChainName(staticDataChainList.ETHEREUM_TESTNET) // Returns "Ethereum Testnet"
 * ```
 */
export const formatChainName = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return formatSolanaChainName(chain)
  }
  if (isEthereumChain(chain)) {
    return formatEthereumChainName(chain)
  }
  return chain
}

/**
 * Get the currency symbol for a blockchain network
 * @param chain Chain type
 * @returns Currency symbol string
 *
 * @example
 * ```typescript
 * getCurrencySymbol(staticDataChainList.SOLANA_MAINNET) // Returns "SOL"
 * getCurrencySymbol(staticDataChainList.ETHEREUM_MAINNET) // Returns "ETH"
 * ```
 */
export const getCurrencySymbol = (chain: TypeChainName): string => {
  if (isSolanaChain(chain)) {
    return getSolanaCurrencySymbol(chain)
  }
  if (isEthereumChain(chain)) {
    return getEthereumCurrencySymbol(chain)
  }
  return "TOKEN"
}

// ============================================================================
// SEED GENERATION - Moved from seed-generator.ts
// ============================================================================

/**
 * BIP39 word list subset for demonstration purposes
 * In production, use the complete BIP39 word list from a trusted source
 */
const DEMO_WORD_LIST = [
  "abandon",
  "ability",
  "able",
  "about",
  "above",
  "absent",
  "absorb",
  "abstract",
  "absurd",
  "abuse",
  "access",
  "accident",
  "account",
  "accuse",
  "achieve",
  "acid",
  "acoustic",
  "acquire",
  "across",
  "act",
  "action",
  "actor",
  "actress",
  "actual",
  "adapt",
  "add",
  "addict",
  "address",
  "adjust",
  "admit",
  "adult",
  "advance",
  "advice",
  "aerobic",
  "affair",
  "afford",
  "afraid",
  "again",
  "against",
  "age",
  "agent",
  "agree",
  "ahead",
  "aim",
  "air",
  "airport",
  "aisle",
  "alarm",
  "album",
  "alcohol",
  "alert",
  "alien",
  "all",
  "alley",
  "allow",
  "almost",
  "alone",
  "alpha",
  "already",
  "also",
  "alter",
  "always",
  "amateur",
  "amazing",
  "among",
  "amount",
  "amused",
  "analyst",
  "anchor",
  "ancient",
  "anger",
  "angle",
  "angry",
  "animal",
  "ankle",
  "announce",
  "annual",
  "another",
  "answer",
  "antenna",
  "antique",
  "anxiety",
  "any",
  "apart",
  "apology",
  "appear",
  "apple",
  "approve",
  "april",
  "arch",
  "arctic",
  "area",
  "arena",
  "argue",
  "arm",
  "armed",
  "armor",
  "army",
  "around",
  "arrange",
  "arrest",
  "arrive",
  "arrow",
  "art",
  "artefact",
  "artist",
  "artwork",
  "ask",
  "aspect",
  "assault",
  "asset",
  "assist",
  "assume",
  "asthma",
  "athlete",
  "atom",
  "attack",
  "attend",
  "attitude",
  "attract",
  "auction",
  "audit",
  "august",
  "aunt",
  "author",
  "auto",
  "autumn",
  "average",
  "avocado",
  "avoid",
  "awake",
  "aware",
  "away",
  "awesome",
  "awful",
  "awkward"
]

/**
 * Generate a mock seed phrase for demonstration purposes
 * @param wordCount Number of words in the seed phrase (12, 15, 18, 21, or 24)
 * @returns Mock seed phrase string
 *
 * @example
 * ```typescript
 * generateMockSeed() // Returns "abandon ability able about above absent absorb abstract absurd abuse access accident"
 * generateMockSeed(24) // Returns 24-word seed phrase
 * ```
 *
 * @warning This is for demonstration only. Use proper cryptographic libraries in production.
 */
export const generateMockSeed = (wordCount: 12 | 15 | 18 | 21 | 24 = 12): string => {
  if (![12, 15, 18, 21, 24].includes(wordCount)) {
    throw new Error("Invalid word count. Must be 12, 15, 18, 21, or 24")
  }

  const selectedWords: string[] = []
  const usedIndices = new Set<number>()

  // Generate unique random words
  while (selectedWords.length < wordCount) {
    const randomIndex = Math.floor(Math.random() * DEMO_WORD_LIST.length)

    if (!usedIndices.has(randomIndex)) {
      usedIndices.add(randomIndex)
      const word = DEMO_WORD_LIST[randomIndex]
      if (word) {
        selectedWords.push(word)
      }
    }
  }

  return selectedWords.join(" ")
}

// ============================================================================
// FEE CALCULATION - Uniswap V2 inspired approach
// ============================================================================

/**
 * Calculate fee deduction using your specified mechanism
 * Formula: result = (input * (10000 - calcFee)) / 10000
 * Where calcFee = getFee * 100
 *
 * @param amount Original amount (in smallest unit, e.g., lamports, wei, satoshis)
 * @param feePercentage Fee percentage as string (e.g., "0.3" for 0.3%, "1.5" for 1.5%)
 * @returns Object with amountAfterFee and feeAmount
 *
 * @example
 * ```typescript
 * // Example 1: 0.3% fee on 1 * 10^18 units
 * const input = BigInt(1) * BigInt(10 ** 18)
 * const result = calculateFeeDeduction(input, "0.3")
 * // Formula: (input * (10000 - 30)) / 10000 = (input * 9970) / 10000
 *
 * // Example 2: 0.3% fee on 10,000 units
 * const result2 = calculateFeeDeduction(BigInt(10000), "0.3")
 * // Returns: { amountAfterFee: 9970n, feeAmount: 30n }
 * ```
 */
export const calculateFeeDeduction = (amount: bigint, feePercentage: string | number): { amountAfterFee: bigint; feeAmount: bigint } => {
  if (amount <= 0n) {
    return { amountAfterFee: 0n, feeAmount: 0n }
  }

  // Parse fee percentage
  let getFee: number
  if (typeof feePercentage === "string") {
    getFee = parseFloat(feePercentage)
  } else {
    getFee = feePercentage
  }

  // Validate fee range
  if (isNaN(getFee) || getFee < 0 || getFee > 100) {
    return { amountAfterFee: amount, feeAmount: 0n }
  }

  if (getFee === 0) {
    return { amountAfterFee: amount, feeAmount: 0n }
  }

  // Your specified calculation mechanism:
  // const getFee = 0.3; // from db
  // const calcFee = getFee * 100;
  // const input = 1 * 10 ** 18; // amount input trade
  // const result = (input * (10000 - calcFee)) / 10000;

  const calcFee = Math.floor(getFee * 100) // Convert to basis points (0.3 -> 30)
  const amountAfterFee = (amount * BigInt(10000 - calcFee)) / BigInt(10000)
  const feeAmount = amount - amountAfterFee

  return { amountAfterFee, feeAmount }
}

/**
 * Calculate the amount needed to receive a specific amount after fee deduction
 * This is the inverse of calculateFeeDeduction using your specified mechanism
 *
 * @param desiredAmount Amount you want to receive after fees
 * @param feePercentage Fee percentage as string (e.g., "0.3" for 0.3%, "1.5" for 1.5%)
 * @returns Object with requiredAmount and feeAmount
 *
 * @example
 * ```typescript
 * // Example: How much do I need to send to receive 9,970 after 0.3% fee?
 * const result = calculateRequiredAmount(BigInt(9970), "0.3")
 * // Returns: { requiredAmount: 10000n, feeAmount: 30n }
 * ```
 */
export const calculateRequiredAmount = (desiredAmount: bigint, feePercentage: string | number): { requiredAmount: bigint; feeAmount: bigint } => {
  if (desiredAmount <= 0n) {
    return { requiredAmount: 0n, feeAmount: 0n }
  }

  // Parse fee percentage
  let getFee: number
  if (typeof feePercentage === "string") {
    getFee = parseFloat(feePercentage)
  } else {
    getFee = feePercentage
  }

  // Validate fee range
  if (isNaN(getFee) || getFee < 0 || getFee > 100) {
    return { requiredAmount: desiredAmount, feeAmount: 0n }
  }

  if (getFee === 0) {
    return { requiredAmount: desiredAmount, feeAmount: 0n }
  }

  // Inverse calculation: if result = (input * (10000 - calcFee)) / 10000
  // Then: input = (result * 10000) / (10000 - calcFee)
  const calcFee = Math.floor(getFee * 100) // Convert to basis points (0.3 -> 30)
  const requiredAmount = (desiredAmount * BigInt(10000)) / BigInt(10000 - calcFee)
  const feeAmount = requiredAmount - desiredAmount

  return { requiredAmount, feeAmount }
}

/**
 * Validate fee percentage and convert to basis points
 * @param feePercentage Fee percentage as string or number (0-100)
 * @returns Basis points (0-10000) or null if invalid
 *
 * @example
 * ```typescript
 * validateFeePercentage("0.3")  // Returns 30
 * validateFeePercentage("1.5")  // Returns 150
 * validateFeePercentage("100")  // Returns 10000
 * validateFeePercentage("-1")   // Returns null
 * validateFeePercentage("101")  // Returns null
 * validateFeePercentage(0.3)    // Returns 30
 * ```
 */
export const validateFeePercentage = (feePercentage: string | number): number | null => {
  let fee: number

  if (typeof feePercentage === "string") {
    fee = parseFloat(feePercentage)
  } else if (typeof feePercentage === "number") {
    fee = feePercentage
  } else {
    return null
  }

  if (isNaN(fee) || fee < 0 || fee > 100) {
    return null
  }

  return Math.floor(fee * 100)
}

/**
 * Format fee amount for display with proper decimal places
 * @param feeAmount Fee amount in smallest unit
 * @param decimals Number of decimal places for the token
 * @returns Formatted string
 *
 * @example
 * ```typescript
 * formatFeeAmount(BigInt(30), 4)     // Returns "0.0030"
 * formatFeeAmount(BigInt(15000), 6)  // Returns "0.015000"
 * ```
 */
export const formatFeeAmount = (feeAmount: bigint, decimals: number): string => {
  const divisor = BigInt(10 ** decimals)
  const wholePart = feeAmount / divisor
  const fractionalPart = feeAmount % divisor

  const fractionalStr = fractionalPart.toString().padStart(decimals, "0")
  return `${wholePart}.${fractionalStr}`
}
