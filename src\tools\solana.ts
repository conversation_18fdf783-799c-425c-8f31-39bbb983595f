import { data<PERSON><PERSON><PERSON><PERSON><PERSON>, type <PERSON><PERSON>hain<PERSON>ame } from "../data"

/**
 * Solana-specific blockchain utilities
 *
 * This module provides Solana-specific functionality including address generation,
 * validation, formatting, and network configuration for both mainnet and devnet.
 */

// Solana-specific constants
export const SOLANA_NETWORKS = {
  MAINNET: {
    chainId: "101",
    name: "Solana Mainnet",
    shortName: "SOL",
    rpcEndpoint: "https://api.mainnet-beta.solana.com",
    explorerUrl: "https://explorer.solana.com",
    currency: "SOL",
    emoji: "🌞",
    color: "#9945FF",
    isTestnet: false
  },
  DEVNET: {
    chainId: "103",
    name: "Solana Devnet",
    shortName: "SOL-DEV",
    rpcEndpoint: "https://api.devnet.solana.com",
    explorerUrl: "https://explorer.solana.com/?cluster=devnet",
    currency: "SOL",
    emoji: "🌙",
    color: "#9945FF",
    isTestnet: true
  }
} as const

/**
 * Get Solana network configuration
 * @param chain Solana chain type
 * @returns Network configuration object
 */
export function getSolanaNetworkConfig(chain: TypeChainName) {
  switch (chain) {
    case "solana_mainnet":
      return SOLANA_NETWORKS.MAINNET
    case "solana_devnet":
      return SOLANA_NETWORKS.DEVNET
    default:
      throw new Error(`Invalid Solana chain type: ${chain}`)
  }
}

/**
 * Generate a Solana Base58 address
 * @returns Base58 address string (44 characters)
 *
 * @example
 * ```typescript
 * generateSolanaAddress() // Returns "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"
 * ```
 */
export function generateSolanaAddress(): string {
  const chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
  return Array.from({ length: 44 }, () => chars[Math.floor(Math.random() * chars.length)]).join("")
}

/**
 * Validate Solana address format
 * @param address Address string to validate
 * @returns True if address format is valid for Solana
 *
 * @example
 * ```typescript
 * validateSolanaAddress("7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU") // Returns true
 * validateSolanaAddress("invalid") // Returns false
 * ```
 */
export function validateSolanaAddress(address: string): boolean {
  // Base58 format, typically 32-44 characters
  return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address)
}

/**
 * Get Solana address length constraints
 * @returns Address length range for Solana
 */
export function getSolanaAddressLength(): { min: number; max: number } {
  return { min: 32, max: 44 }
}

/**
 * Get Solana address format description
 * @returns Human-readable description of Solana address format
 */
export function getSolanaAddressFormatDescription(): string {
  return "Base58 encoded public key (32-44 characters)"
}

/**
 * Check if a chain type is Solana (dynamically detected by pattern)
 * @param chain Chain type to check
 * @returns True if the chain is Solana (mainnet or devnet)
 */
export function isSolanaChain(chain: TypeChainName): boolean {
  const chainStr = chain.toLowerCase()
  return chainStr.startsWith("solana_") || chainStr === "solana"
}

/**
 * Get Solana chain ID
 * @param chain Solana chain type
 * @returns Chain ID string
 */
export function getSolanaChainId(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.chainId
}

/**
 * Get Solana RPC endpoint
 * @param chain Solana chain type
 * @returns RPC endpoint URL
 */
export function getSolanaRpcEndpoint(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.rpcEndpoint
}

/**
 * Get Solana explorer URL
 * @param chain Solana chain type
 * @param address Optional address to link to
 * @returns Explorer URL
 */
export function getSolanaExplorerUrl(chain: TypeChainName, address?: string): string {
  const config = getSolanaNetworkConfig(chain)
  return address ? `${config.explorerUrl}/address/${address}` : config.explorerUrl
}

/**
 * Estimate Solana transaction fees
 * @param chain Solana chain type
 * @returns Estimated fee in lamports
 */
export function estimateSolanaTransactionFee(chain: TypeChainName): bigint {
  // Standard Solana transaction fee is 5000 lamports
  return BigInt(5000)
}

/**
 * Format Solana amount with proper decimals
 * @param lamports Amount in lamports (smallest unit)
 * @param decimals Number of decimal places to show
 * @returns Formatted SOL amount
 */
export function formatSolanaAmount(lamports: bigint | string | number, decimals: number = 9): string {
  const lamportsBigInt = typeof lamports === "bigint" ? lamports : BigInt(lamports.toString())
  const sol = Number(lamportsBigInt) / Math.pow(10, 9) // SOL has 9 decimals
  return sol.toFixed(decimals)
}

/**
 * Convert SOL to lamports
 * @param sol Amount in SOL
 * @returns Amount in lamports
 */
export function solToLamports(sol: number | string): bigint {
  const solNumber = typeof sol === "string" ? parseFloat(sol) : sol
  return BigInt(Math.floor(solNumber * Math.pow(10, 9)))
}

/**
 * Convert lamports to SOL
 * @param lamports Amount in lamports
 * @returns Amount in SOL
 */
export function lamportsToSol(lamports: bigint | string | number): number {
  const lamportsBigInt = typeof lamports === "bigint" ? lamports : BigInt(lamports.toString())
  return Number(lamportsBigInt) / Math.pow(10, 9)
}

// ============================================================================
// CHAIN UTILITIES - Solana-specific functions from chain-utils.ts
// ============================================================================

/**
 * Get Solana chain type from string input with support for various aliases
 * @param chainInput Chain input string (case-insensitive)
 * @returns Solana TypeChainName or null if not Solana
 *
 * @example
 * ```typescript
 * getSolanaChainType("solana") // Returns "solana_mainnet"
 * getSolanaChainType("sol") // Returns "solana_mainnet"
 * getSolanaChainType("ethereum") // Returns null
 * ```
 */
export function getSolanaChainType(chainInput: string): TypeChainName | null {
  const solanaChainMap: Record<string, TypeChainName> = {
    solana: "solana_mainnet",
    solana_mainnet: "solana_mainnet",
    solana_devnet: "solana_devnet",
    sol: "solana_mainnet"
  }

  return solanaChainMap[chainInput.toLowerCase()] || null
}

/**
 * Check if a chain type is Solana mainnet
 * @param chain Chain type to check
 * @returns True if the chain is Solana mainnet
 */
export function isSolanaMainnet(chain: TypeChainName): boolean {
  return chain === "solana_mainnet"
}

/**
 * Check if a chain type is Solana devnet (testnet)
 * @param chain Chain type to check
 * @returns True if the chain is Solana devnet
 */
export function isSolanaDevnet(chain: TypeChainName): boolean {
  return chain === "solana_devnet"
}

/**
 * Get all Solana chain types (dynamically detected from dataChainList)
 * @returns Array of Solana chain types
 */
export function getAllSolanaChains(): TypeChainName[] {
  return Object.keys(dataChainList).filter((chain) => isSolanaChain(chain as TypeChainName)) as TypeChainName[]
}

// ============================================================================
// CHAIN FORMATTING - Solana-specific functions from chain-formatter.ts
// ============================================================================

/**
 * Format Solana chain name for user-friendly display
 * @param chain Solana chain type to format
 * @returns Formatted, human-readable chain name
 *
 * @example
 * ```typescript
 * formatSolanaChainName("solana_mainnet") // Returns "Solana Mainnet"
 * formatSolanaChainName("solana_devnet") // Returns "Solana Devnet"
 * ```
 */
export function formatSolanaChainName(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.name
}

/**
 * Get the currency symbol for Solana networks
 * @param chain Solana chain type
 * @returns Currency symbol string (always "SOL")
 */
export function getSolanaCurrencySymbol(chain: TypeChainName): string {
  return "SOL"
}

/**
 * Get the short name/code for Solana chains
 * @param chain Solana chain type
 * @returns Short chain code
 */
export function getSolanaShortName(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.shortName
}

/**
 * Get the emoji icon for Solana networks
 * @param chain Solana chain type
 * @returns Emoji string representing the chain
 */
export function getSolanaEmoji(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.emoji
}

/**
 * Get the color code associated with Solana networks
 * @param chain Solana chain type
 * @returns Hex color code string
 */
export function getSolanaColor(chain: TypeChainName): string {
  const config = getSolanaNetworkConfig(chain)
  return config.color
}

/**
 * Format Solana chain with emoji and name for display
 * @param chain Solana chain type
 * @returns Formatted string with emoji and name
 */
export function formatSolanaChainWithEmoji(chain: TypeChainName): string {
  return `${getSolanaEmoji(chain)} ${formatSolanaChainName(chain)}`
}

/**
 * Format Solana currency amount with symbol
 * @param amount Amount to format (as string or number)
 * @param chain Solana chain type
 * @param decimals Number of decimal places to show
 * @returns Formatted amount with currency symbol
 */
export function formatSolanaCurrencyAmount(amount: string | number | bigint, chain: TypeChainName, decimals: number = 2): string {
  const symbol = getSolanaCurrencySymbol(chain)

  if (typeof amount === "bigint") {
    return `${amount.toString()} ${symbol}`
  }

  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount

  if (isNaN(numAmount)) {
    return `0 ${symbol}`
  }

  return `${numAmount.toFixed(decimals)} ${symbol}`
}

/**
 * Get network type (mainnet/testnet) for Solana chains
 * @param chain Solana chain type
 * @returns Network type string
 */
export function getSolanaNetworkType(chain: TypeChainName): "mainnet" | "testnet" {
  return chain === "solana_devnet" ? "testnet" : "mainnet"
}

/**
 * Get blockchain family for Solana (always returns "solana")
 * @param chain Solana chain type
 * @returns Blockchain family name
 */
export function getSolanaBlockchainFamily(chain: TypeChainName): string {
  return "solana"
}

/**
 * Format Solana chain information for detailed display
 * @param chain Solana chain type
 * @returns Object with formatted chain information
 */
export function getSolanaChainDisplayInfo(chain: TypeChainName) {
  return {
    name: formatSolanaChainName(chain),
    symbol: getSolanaCurrencySymbol(chain),
    shortName: getSolanaShortName(chain),
    emoji: getSolanaEmoji(chain),
    color: getSolanaColor(chain),
    networkType: getSolanaNetworkType(chain),
    family: getSolanaBlockchainFamily(chain),
    withEmoji: formatSolanaChainWithEmoji(chain)
  }
}

// ============================================================================
// ADDRESS GENERATION - Solana-specific functions from address-generator.ts
// ============================================================================

/**
 * Generate a mock Solana address for the specified chain
 * @param chain Solana chain type
 * @returns Mock Solana address string in Base58 format
 *
 * @example
 * ```typescript
 * generateMockSolanaAddress("solana_mainnet") // Returns Base58 address
 * generateMockSolanaAddress("solana_devnet") // Returns Base58 address
 * ```
 */
export function generateMockSolanaAddress(chain: TypeChainName): string {
  if (!isSolanaChain(chain)) {
    throw new Error(`Invalid Solana chain type: ${chain}`)
  }
  return generateSolanaAddress()
}

/**
 * Generate a Base58 encoded address (alias for generateSolanaAddress)
 * @returns Base58 address string (44 characters)
 *
 * @example
 * ```typescript
 * generateBase58Address() // Returns "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"
 * ```
 */
export function generateBase58Address(): string {
  return generateSolanaAddress()
}

/**
 * Validate if an address matches the Solana format
 * @param address Address string to validate
 * @param chain Solana chain type to validate against
 * @returns True if address format is valid for Solana
 *
 * @example
 * ```typescript
 * isValidSolanaAddressFormat("7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", "solana_mainnet") // Returns true
 * isValidSolanaAddressFormat("invalid", "solana_mainnet") // Returns false
 * ```
 */
export function isValidSolanaAddressFormat(address: string, chain: TypeChainName): boolean {
  if (!isSolanaChain(chain)) {
    return false
  }
  return validateSolanaAddress(address)
}

/**
 * Get the expected address length for Solana chains
 * @param chain Solana chain type
 * @returns Expected address length range for Solana
 */
export function getSolanaAddressLengthForChain(chain: TypeChainName): { min: number; max: number } {
  if (!isSolanaChain(chain)) {
    return { min: 0, max: 0 }
  }
  return getSolanaAddressLength()
}

/**
 * Get the address format description for Solana chains
 * @param chain Solana chain type
 * @returns Human-readable description of Solana address format
 */
export function getSolanaAddressFormatDescriptionForChain(chain: TypeChainName): string {
  if (!isSolanaChain(chain)) {
    return "Unknown address format"
  }
  return getSolanaAddressFormatDescription()
}
