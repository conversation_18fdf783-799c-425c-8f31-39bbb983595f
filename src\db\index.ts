/**
 * Database connection configuration
 *
 * This file sets up the database connection and exports the database instance
 * along with schema tables for use throughout the application.
 */

import postgres from "postgres"
import { drizzle } from "drizzle-orm/postgres-js"
import { tableUsers } from "./schema/users"
import { tableWallets } from "./schema/wallets"
import { tradingHistory } from "./schema/trading_history"
import { tableFees } from "./schema/fees"

const uri = process.env.DATABASE_URI || `postgres://postgres:postgres@localhost:5432/postgres`
const client = postgres(uri, { prepare: false })
const db = drizzle({
  client,
  schema: {
    users: tableUsers,
    wallets: tableWallets,
    tradingHistory,
    fees: tableFees
  },
  casing: "snake_case"
})

// Export database instance
export { db }
