// import { drizzle } from "drizzle-orm/postgres-js"
// import postgres from "postgres"

import { readdirSync, readFileSync, unlinkSync, writeFileSync } from "node:fs"
import { join } from "node:path"

// // Disable prefetch as it is not supported for "Transaction" pool mode
// const strPooler = process.env.DATABASE_URI || ``
// const client = postgres(strPooler, { prepare: false })
// const db = drizzle(client)
// const run = async () => {
//   try {
//     const res = await db.execute(`SELECT name FROM pg_timezone_names`)
//     console.log({ res })
//   } catch (err) {
//     console.log(err)
//   }
// }

// run()

const allDir = readdirSync(join(process.cwd(), `content`), { encoding: `utf8` })
allDir.forEach((fileName) => {
  if (fileName.endsWith(`.md`) === true) {
    // update file name from .md to html
    const newFileName = fileName.replace(`.md`, `.html`)
    const oldPath = join(process.cwd(), `content`, fileName)
    const newPath = join(process.cwd(), `content`, newFileName)

    const oldValue = readFileSync(oldPath, { encoding: `utf8` })
    writeFileSync(newPath, oldValue, { encoding: `utf8` })

    unlinkSync(oldPath)
    console.log(`Renamed ${oldPath} to ${newPath}`)
  }
})
