import { data<PERSON>hain<PERSON>ist, dataChainName, type Type<PERSON>hainName } from "../data"

/**
 * Ethereum-specific blockchain utilities
 *
 * This module provides Ethereum-specific functionality including address generation,
 * validation, formatting, and network configuration for both mainnet and testnet.
 */

/**
 * Ethereum network configuration class
 */
export class EthereumConfig {
  // Ethereum-specific constants
  static readonly NETWORKS = {
    MAINNET: {
      chainId: "1",
      name: "Ethereum Mainnet",
      shortName: "ETH",
      rpcEndpoint: "https://mainnet.infura.io/v3/",
      explorerUrl: "https://etherscan.io",
      currency: "ETH",
      decimals: 18
    },
    TESTNET: {
      chainId: "11155111", // Sepolia testnet
      name: "Ethereum Testnet",
      shortName: "ETH-TEST",
      rpcEndpoint: "https://sepolia.infura.io/v3/",
      explorerUrl: "https://sepolia.etherscan.io",
      currency: "ETH",
      decimals: 18
    }
  } as const

  /**
   * Get Ethereum network configuration
   * @param chain Ethereum chain type
   * @returns Network configuration object
   */
  static getNetworkConfig = (chain: TypeChainName) => {
    switch (chain) {
      case dataChainList.ethereum_mainnet:
        return EthereumConfig.NETWORKS.MAINNET
      case dataChainList.ethereum_testnet:
        return EthereumConfig.NETWORKS.TESTNET
      default:
        throw new Error(`Invalid Ethereum chain type: ${chain}`)
    }
  }

  /**
   * Get Ethereum chain ID
   * @param chain Ethereum chain type
   * @returns Chain ID string
   */
  static getChainId = (chain: TypeChainName): string => {
    const config = EthereumConfig.getNetworkConfig(chain)
    return config.chainId
  }

  /**
   * Get Ethereum RPC endpoint
   * @param chain Ethereum chain type
   * @param apiKey Optional API key for Infura
   * @returns RPC endpoint URL
   */
  static getRpcEndpoint = (chain: TypeChainName, apiKey?: string): string => {
    const config = EthereumConfig.getNetworkConfig(chain)
    return apiKey ? `${config.rpcEndpoint}${apiKey}` : config.rpcEndpoint
  }

  /**
   * Get Ethereum explorer URL
   * @param chain Ethereum chain type
   * @param address Optional address to link to
   * @returns Explorer URL
   */
  static getExplorerUrl = (chain: TypeChainName, address?: string): string => {
    const config = EthereumConfig.getNetworkConfig(chain)
    return address ? `${config.explorerUrl}/address/${address}` : config.explorerUrl
  }
}

/**
 * Ethereum address utilities class
 */
export class EthereumAddress {
  /**
   * Generate an Ethereum-style hexadecimal address
   * @returns Ethereum address string (42 characters including 0x prefix)
   *
   * @example
   * ```typescript
   * EthereumAddress.generate() // Returns "******************************************"
   * ```
   */
  static generate = (): string => {
    const chars = "0123456789abcdef"
    return "0x" + Array.from({ length: 40 }, () => chars[Math.floor(Math.random() * chars.length)]).join("")
  }

  /**
   * Validate Ethereum address format
   * @param address Address string to validate
   * @returns True if address format is valid for Ethereum
   *
   * @example
   * ```typescript
   * EthereumAddress.validate("******************************************") // Returns true
   * EthereumAddress.validate("invalid") // Returns false
   * ```
   */
  static validate = (address: string): boolean => {
    // Ethereum hex format, 42 characters with 0x prefix
    return /^0x[a-fA-F0-9]{40}$/.test(address)
  }

  /**
   * Get Ethereum address length constraints
   * @returns Address length for Ethereum (fixed at 42 characters)
   */
  static getLength = (): { min: number; max: number } => {
    return { min: 42, max: 42 } // Fixed length with 0x prefix
  }

  /**
   * Get Ethereum address format description
   * @returns Human-readable description of Ethereum address format
   */
  static getFormatDescription = (): string => {
    return "Hexadecimal address with 0x prefix (42 characters)"
  }

  /**
   * Generate a mock Ethereum address for the specified chain
   * @param chain Ethereum chain type
   * @returns Mock Ethereum address string in hex format
   *
   * @example
   * ```typescript
   * EthereumAddress.generateMock("ethereum_mainnet") // Returns hex address
   * EthereumAddress.generateMock("ethereum_testnet") // Returns hex address
   * ```
   */
  static generateMock = (chain: TypeChainName): string => {
    if (!EthereumChain.isEthereumChain(chain)) {
      throw new Error(`Invalid Ethereum chain type: ${chain}`)
    }
    return EthereumAddress.generate()
  }

  /**
   * Validate if an address matches the Ethereum format for a specific chain
   * @param address Address string to validate
   * @param chain Ethereum chain type to validate against
   * @returns True if address format is valid for Ethereum
   *
   * @example
   * ```typescript
   * EthereumAddress.validateForChain("******************************************", "ethereum_mainnet") // Returns true
   * EthereumAddress.validateForChain("invalid", "ethereum_mainnet") // Returns false
   * ```
   */
  static validateForChain = (address: string, chain: TypeChainName): boolean => {
    if (!EthereumChain.isEthereumChain(chain)) {
      return false
    }
    return EthereumAddress.validate(address)
  }

  /**
   * Get the expected address length for Ethereum chains
   * @param chain Ethereum chain type
   * @returns Expected address length for Ethereum (fixed at 42 characters)
   */
  static getLengthForChain = (chain: TypeChainName): { min: number; max: number } => {
    if (!EthereumChain.isEthereumChain(chain)) {
      return { min: 0, max: 0 }
    }
    return EthereumAddress.getLength()
  }

  /**
   * Get the address format description for Ethereum chains
   * @param chain Ethereum chain type
   * @returns Human-readable description of Ethereum address format
   */
  static getFormatDescriptionForChain = (chain: TypeChainName): string => {
    if (!EthereumChain.isEthereumChain(chain)) {
      return "Unknown address format"
    }
    return EthereumAddress.getFormatDescription()
  }
}

/**
 * Ethereum chain utilities class
 */
export class EthereumChain {
  /**
   * Check if a chain type is Ethereum (dynamically detected by pattern)
   * @param chain Chain type to check
   * @returns True if the chain is Ethereum (mainnet or testnet)
   */
  static isEthereumChain = (chain: TypeChainName): boolean => {
    const chainStr = chain.toLowerCase()
    return chainStr.startsWith("ethereum_") || chainStr === "ethereum"
  }

  /**
   * Get Ethereum chain type from string input with support for various aliases
   * @param chainInput Chain input string (case-insensitive)
   * @returns Ethereum TypeChainName or null if not Ethereum
   *
   * @example
   * ```typescript
   * EthereumChain.getChainType("ethereum") // Returns "ethereum_mainnet"
   * EthereumChain.getChainType("eth") // Returns "ethereum_mainnet"
   * EthereumChain.getChainType("solana") // Returns null
   * ```
   */
  static getChainType = (chainInput: string): TypeChainName | null => {
    chainInput = chainInput.toLowerCase()
    let res = null
    if (chainInput == `eth` || chainInput == `ethereum` || chainInput == `ethereum_mainnet`) res = `ethereum_mainnet`
    if (chainInput == `ethereum_testnet`) res = `ethereum_testnet`
    return res as TypeChainName
  }

  /**
   * Check if a chain type is Ethereum mainnet
   * @param chain Chain type to check
   * @returns True if the chain is Ethereum mainnet
   */
  static isMainnet = (chain: TypeChainName): boolean => {
    return chain === dataChainList.ethereum_mainnet
  }

  /**
   * Check if a chain type is Ethereum testnet
   * @param chain Chain type to check
   * @returns True if the chain is Ethereum testnet
   */
  static isTestnet = (chain: TypeChainName): boolean => {
    return chain === dataChainList.ethereum_testnet
  }

  /**
   * Get all Ethereum chain types (dynamically detected from dataChainList enum)
   * @returns Array of Ethereum chain types
   */
  static getAllChains = (): TypeChainName[] => {
    return dataChainName.filter((chain) => EthereumChain.isEthereumChain(chain as any)) as any
  }

  /**
   * Get network type (mainnet/testnet) for Ethereum chains
   * @param chain Ethereum chain type
   * @returns Network type string
   */
  static getNetworkType = (chain: TypeChainName): "mainnet" | "testnet" => {
    return chain === dataChainList.ethereum_testnet ? "testnet" : "mainnet"
  }

  /**
   * Get blockchain family for Ethereum (always returns "ethereum")
   * @param chain Ethereum chain type
   * @returns Blockchain family name
   */
  static getBlockchainFamily = (chain: TypeChainName): string => {
    return "ethereum"
  }
}

/**
 * Ethereum amount conversion utilities class
 */
export class EthereumAmount {
  /**
   * Format Ethereum amount with proper decimals
   * @param wei Amount in wei (smallest unit)
   * @param decimals Number of decimal places to show
   * @returns Formatted ETH amount
   */
  static format = (wei: bigint | string | number, decimals: number = 18): string => {
    const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
    const eth = Number(weiBigInt) / Math.pow(10, 18) // ETH has 18 decimals
    return eth.toFixed(decimals)
  }

  /**
   * Convert ETH to wei
   * @param eth Amount in ETH
   * @returns Amount in wei
   */
  static ethToWei = (eth: number | string): bigint => {
    const ethNumber = typeof eth === "string" ? parseFloat(eth) : eth
    return BigInt(Math.floor(ethNumber * Math.pow(10, 18)))
  }

  /**
   * Convert wei to ETH
   * @param wei Amount in wei
   * @returns Amount in ETH
   */
  static weiToEth = (wei: bigint | string | number): number => {
    const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
    return Number(weiBigInt) / Math.pow(10, 18)
  }

  /**
   * Convert gwei to wei
   * @param gwei Amount in gwei
   * @returns Amount in wei
   */
  static gweiToWei = (gwei: number | string): bigint => {
    const gweiNumber = typeof gwei === "string" ? parseFloat(gwei) : gwei
    return BigInt(Math.floor(gweiNumber * Math.pow(10, 9)))
  }

  /**
   * Convert wei to gwei
   * @param wei Amount in wei
   * @returns Amount in gwei
   */
  static weiToGwei = (wei: bigint | string | number): number => {
    const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
    return Number(weiBigInt) / Math.pow(10, 9)
  }

  /**
   * Format Ethereum currency amount with symbol
   * @param amount Amount to format (as string or number)
   * @param chain Ethereum chain type
   * @param decimals Number of decimal places to show
   * @returns Formatted amount with currency symbol
   */
  static formatCurrency = (amount: string | number | bigint, chain: TypeChainName, decimals: number = 2): string => {
    const symbol = EthereumFormatter.getCurrencySymbol(chain)

    if (typeof amount === "bigint") {
      return `${amount.toString()} ${symbol}`
    }

    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount

    if (isNaN(numAmount)) {
      return `0 ${symbol}`
    }

    return `${numAmount.toFixed(decimals)} ${symbol}`
  }
}

/**
 * Ethereum transaction utilities class
 */
export class EthereumTransaction {
  /**
   * Validate Ethereum transaction hash
   * @param hash Transaction hash to validate
   * @returns True if hash format is valid
   */
  static validateTxHash = (hash: string): boolean => {
    return /^0x[a-fA-F0-9]{64}$/.test(hash)
  }
}

/**
 * Ethereum formatting utilities class
 */
export class EthereumFormatter {
  /**
   * Format Ethereum chain name for user-friendly display
   * @param chain Ethereum chain type to format
   * @returns Formatted, human-readable chain name
   *
   * @example
   * ```typescript
   * EthereumFormatter.formatChainName("ethereum_mainnet") // Returns "Ethereum Mainnet"
   * EthereumFormatter.formatChainName("ethereum_testnet") // Returns "Ethereum Testnet"
   * ```
   */
  static formatChainName = (chain: TypeChainName): string => {
    const config = EthereumConfig.getNetworkConfig(chain)
    return config.name
  }

  /**
   * Get the currency symbol for Ethereum networks
   * @param chain Ethereum chain type
   * @returns Currency symbol string (always "ETH")
   */
  static getCurrencySymbol = (chain: TypeChainName): string => {
    return "ETH"
  }

  /**
   * Get the short name/code for Ethereum chains
   * @param chain Ethereum chain type
   * @returns Short chain code
   */
  static getShortName = (chain: TypeChainName): string => {
    const config = EthereumConfig.getNetworkConfig(chain)
    return config.shortName
  }

  /**
   * Format Ethereum chain information for detailed display
   * @param chain Ethereum chain type
   * @returns Object with formatted chain information
   */
  static getChainDisplayInfo = (chain: TypeChainName) => {
    return {
      name: EthereumFormatter.formatChainName(chain),
      symbol: EthereumFormatter.getCurrencySymbol(chain),
      shortName: EthereumFormatter.getShortName(chain),
      networkType: EthereumChain.getNetworkType(chain),
      family: EthereumChain.getBlockchainFamily(chain)
    }
  }
}

// ============================================================================
// BACKWARD COMPATIBILITY EXPORTS
// ============================================================================

// Export class static methods as standalone functions for backward compatibility
export const getEthereumNetworkConfig = EthereumConfig.getNetworkConfig
export const getEthereumChainId = EthereumConfig.getChainId
export const getEthereumRpcEndpoint = EthereumConfig.getRpcEndpoint
export const getEthereumExplorerUrl = EthereumConfig.getExplorerUrl

export const generateEthereumAddress = EthereumAddress.generate
export const validateEthereumAddress = EthereumAddress.validate
export const getEthereumAddressLength = EthereumAddress.getLength
export const getEthereumAddressFormatDescription = EthereumAddress.getFormatDescription
export const generateMockEthereumAddress = EthereumAddress.generateMock
export const isValidEthereumAddressFormat = EthereumAddress.validateForChain
export const getEthereumAddressLengthForChain = EthereumAddress.getLengthForChain
export const getEthereumAddressFormatDescriptionForChain = EthereumAddress.getFormatDescriptionForChain

export const isEthereumChain = EthereumChain.isEthereumChain
export const getEthereumChainType = EthereumChain.getChainType
export const isEthereumMainnet = EthereumChain.isMainnet
export const isEthereumTestnet = EthereumChain.isTestnet
export const getAllEthereumChains = EthereumChain.getAllChains
export const getEthereumNetworkType = EthereumChain.getNetworkType
export const getEthereumBlockchainFamily = EthereumChain.getBlockchainFamily

export const formatEthereumAmount = EthereumAmount.format
export const ethToWei = EthereumAmount.ethToWei
export const weiToEth = EthereumAmount.weiToEth
export const gweiToWei = EthereumAmount.gweiToWei
export const weiToGwei = EthereumAmount.weiToGwei
export const formatEthereumCurrencyAmount = EthereumAmount.formatCurrency

export const validateEthereumTxHash = EthereumTransaction.validateTxHash

export const formatEthereumChainName = EthereumFormatter.formatChainName
export const getEthereumCurrencySymbol = EthereumFormatter.getCurrencySymbol
export const getEthereumShortName = EthereumFormatter.getShortName
export const getEthereumChainDisplayInfo = EthereumFormatter.getChainDisplayInfo
