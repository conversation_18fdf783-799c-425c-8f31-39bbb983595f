import { data<PERSON>hain<PERSON>ist, dataChainName, type Type<PERSON>hainName } from "../data"

/**
 * Ethereum-specific blockchain utilities
 *
 * This module provides Ethereum-specific functionality including address generation,
 * validation, formatting, and network configuration for both mainnet and testnet.
 */

// Ethereum-specific constants
export const ETHEREUM_NETWORKS = {
  MAINNET: {
    chainId: "1",
    name: "Ethereum Mainnet",
    shortName: "ETH",
    rpcEndpoint: "https://mainnet.infura.io/v3/",
    explorerUrl: "https://etherscan.io",
    currency: "ETH",
    gasLimit: 21000,
    decimals: 18
  },
  TESTNET: {
    chainId: "11155111", // Sepolia testnet
    name: "Ethereum Testnet",
    shortName: "ETH-TEST",
    rpcEndpoint: "https://sepolia.infura.io/v3/",
    explorerUrl: "https://sepolia.etherscan.io",
    currency: "ETH",
    gasLimit: 21000,
    decimals: 18
  }
} as const

/**
 * Get Ethereum network configuration
 * @param chain Ethereum chain type
 * @returns Network configuration object
 */
export function getEthereumNetworkConfig(chain: TypeChainName) {
  switch (chain) {
    case dataChainList.ethereum_mainnet:
      return ETHEREUM_NETWORKS.MAINNET
    case dataChainList.ethereum_testnet:
      return ETHEREUM_NETWORKS.TESTNET
    default:
      throw new Error(`Invalid Ethereum chain type: ${chain}`)
  }
}

/**
 * Generate an Ethereum-style hexadecimal address
 * @returns Ethereum address string (42 characters including 0x prefix)
 *
 * @example
 * ```typescript
 * generateEthereumAddress() // Returns "******************************************"
 * ```
 */
export function generateEthereumAddress(): string {
  const chars = "0123456789abcdef"
  return "0x" + Array.from({ length: 40 }, () => chars[Math.floor(Math.random() * chars.length)]).join("")
}

/**
 * Validate Ethereum address format
 * @param address Address string to validate
 * @returns True if address format is valid for Ethereum
 *
 * @example
 * ```typescript
 * validateEthereumAddress("******************************************") // Returns true
 * validateEthereumAddress("invalid") // Returns false
 * ```
 */
export function validateEthereumAddress(address: string): boolean {
  // Ethereum hex format, 42 characters with 0x prefix
  return /^0x[a-fA-F0-9]{40}$/.test(address)
}

/**
 * Get Ethereum address length constraints
 * @returns Address length for Ethereum (fixed at 42 characters)
 */
export function getEthereumAddressLength(): { min: number; max: number } {
  return { min: 42, max: 42 } // Fixed length with 0x prefix
}

/**
 * Get Ethereum address format description
 * @returns Human-readable description of Ethereum address format
 */
export function getEthereumAddressFormatDescription(): string {
  return "Hexadecimal address with 0x prefix (42 characters)"
}

/**
 * Check if a chain type is Ethereum (dynamically detected by pattern)
 * @param chain Chain type to check
 * @returns True if the chain is Ethereum (mainnet or testnet)
 */
export function isEthereumChain(chain: TypeChainName): boolean {
  const chainStr = chain.toLowerCase()
  return chainStr.startsWith("ethereum_") || chainStr === "ethereum"
}

/**
 * Get Ethereum chain ID
 * @param chain Ethereum chain type
 * @returns Chain ID string
 */
export function getEthereumChainId(chain: TypeChainName): string {
  const config = getEthereumNetworkConfig(chain)
  return config.chainId
}

/**
 * Get Ethereum RPC endpoint
 * @param chain Ethereum chain type
 * @param apiKey Optional API key for Infura
 * @returns RPC endpoint URL
 */
export function getEthereumRpcEndpoint(chain: TypeChainName, apiKey?: string): string {
  const config = getEthereumNetworkConfig(chain)
  return apiKey ? `${config.rpcEndpoint}${apiKey}` : config.rpcEndpoint
}

/**
 * Get Ethereum explorer URL
 * @param chain Ethereum chain type
 * @param address Optional address to link to
 * @returns Explorer URL
 */
export function getEthereumExplorerUrl(chain: TypeChainName, address?: string): string {
  const config = getEthereumNetworkConfig(chain)
  return address ? `${config.explorerUrl}/address/${address}` : config.explorerUrl
}

/**
 * Format Ethereum amount with proper decimals
 * @param wei Amount in wei (smallest unit)
 * @param decimals Number of decimal places to show
 * @returns Formatted ETH amount
 */
export function formatEthereumAmount(wei: bigint | string | number, decimals: number = 18): string {
  const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
  const eth = Number(weiBigInt) / Math.pow(10, 18) // ETH has 18 decimals
  return eth.toFixed(decimals)
}

/**
 * Convert ETH to wei
 * @param eth Amount in ETH
 * @returns Amount in wei
 */
export function ethToWei(eth: number | string): bigint {
  const ethNumber = typeof eth === "string" ? parseFloat(eth) : eth
  return BigInt(Math.floor(ethNumber * Math.pow(10, 18)))
}

/**
 * Convert wei to ETH
 * @param wei Amount in wei
 * @returns Amount in ETH
 */
export function weiToEth(wei: bigint | string | number): number {
  const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
  return Number(weiBigInt) / Math.pow(10, 18)
}

/**
 * Convert gwei to wei
 * @param gwei Amount in gwei
 * @returns Amount in wei
 */
export function gweiToWei(gwei: number | string): bigint {
  const gweiNumber = typeof gwei === "string" ? parseFloat(gwei) : gwei
  return BigInt(Math.floor(gweiNumber * Math.pow(10, 9)))
}

/**
 * Convert wei to gwei
 * @param wei Amount in wei
 * @returns Amount in gwei
 */
export function weiToGwei(wei: bigint | string | number): number {
  const weiBigInt = typeof wei === "bigint" ? wei : BigInt(wei.toString())
  return Number(weiBigInt) / Math.pow(10, 9)
}

/**
 * Validate Ethereum transaction hash
 * @param hash Transaction hash to validate
 * @returns True if hash format is valid
 */
export function validateEthereumTxHash(hash: string): boolean {
  return /^0x[a-fA-F0-9]{64}$/.test(hash)
}

// ============================================================================
// CHAIN UTILITIES - Ethereum-specific functions from chain-utils.ts
// ============================================================================

/**
 * Get Ethereum chain type from string input with support for various aliases
 * @param chainInput Chain input string (case-insensitive)
 * @returns Ethereum TypeChainName or null if not Ethereum
 *
 * @example
 * ```typescript
 * getEthereumChainType("ethereum") // Returns staticDataChainList.ETHEREUM_MAINNET
 * getEthereumChainType("eth") // Returns staticDataChainList.ETHEREUM_MAINNET
 * getEthereumChainType("solana") // Returns null
 * ```
 */
export function getEthereumChainType(chainInput: string): TypeChainName | null {
  chainInput = chainInput.toLowerCase()
  let res = null
  if (chainInput == `eth` || chainInput == `ethereum` || chainInput == `ethereum_mainnet`) res = `ethereum_mainnet`
  if (chainInput == `ethereum_testnet`) res = `ethereum_testnet`
  return res as TypeChainName
}

/**
 * Check if a chain type is Ethereum mainnet
 * @param chain Chain type to check
 * @returns True if the chain is Ethereum mainnet
 */
export function isEthereumMainnet(chain: TypeChainName): boolean {
  return chain === dataChainList.ethereum_mainnet
}

/**
 * Check if a chain type is Ethereum testnet
 * @param chain Chain type to check
 * @returns True if the chain is Ethereum testnet
 */
export function isEthereumTestnet(chain: TypeChainName): boolean {
  return chain === dataChainList.ethereum_testnet
}

/**
 * Get all Ethereum chain types (dynamically detected from dataChainList enum)
 * @returns Array of Ethereum chain types
 */
export function getAllEthereumChains(): TypeChainName[] {
  return dataChainName.filter((chain) => isEthereumChain(chain as any)) as any
}

// ============================================================================
// CHAIN FORMATTING - Ethereum-specific functions from chain-formatter.ts
// ============================================================================

/**
 * Format Ethereum chain name for user-friendly display
 * @param chain Ethereum chain type to format
 * @returns Formatted, human-readable chain name
 *
 * @example
 * ```typescript
 * formatEthereumChainName(staticDataChainList.ETHEREUM_MAINNET) // Returns "Ethereum Mainnet"
 * formatEthereumChainName(staticDataChainList.ETHEREUM_TESTNET) // Returns "Ethereum Testnet"
 * ```
 */
export function formatEthereumChainName(chain: TypeChainName): string {
  const config = getEthereumNetworkConfig(chain)
  return config.name
}

/**
 * Get the currency symbol for Ethereum networks
 * @param chain Ethereum chain type
 * @returns Currency symbol string (always "ETH")
 */
export function getEthereumCurrencySymbol(chain: TypeChainName): string {
  return "ETH"
}

/**
 * Get the short name/code for Ethereum chains
 * @param chain Ethereum chain type
 * @returns Short chain code
 */
export function getEthereumShortName(chain: TypeChainName): string {
  const config = getEthereumNetworkConfig(chain)
  return config.shortName
}

/**
 * Format Ethereum currency amount with symbol
 * @param amount Amount to format (as string or number)
 * @param chain Ethereum chain type
 * @param decimals Number of decimal places to show
 * @returns Formatted amount with currency symbol
 */
export function formatEthereumCurrencyAmount(amount: string | number | bigint, chain: TypeChainName, decimals: number = 2): string {
  const symbol = getEthereumCurrencySymbol(chain)

  if (typeof amount === "bigint") {
    return `${amount.toString()} ${symbol}`
  }

  const numAmount = typeof amount === "string" ? parseFloat(amount) : amount

  if (isNaN(numAmount)) {
    return `0 ${symbol}`
  }

  return `${numAmount.toFixed(decimals)} ${symbol}`
}

/**
 * Get network type (mainnet/testnet) for Ethereum chains
 * @param chain Ethereum chain type
 * @returns Network type string
 */
export function getEthereumNetworkType(chain: TypeChainName): "mainnet" | "testnet" {
  return chain === dataChainList.ethereum_testnet ? "testnet" : "mainnet"
}

/**
 * Get blockchain family for Ethereum (always returns "ethereum")
 * @param chain Ethereum chain type
 * @returns Blockchain family name
 */
export function getEthereumBlockchainFamily(chain: TypeChainName): string {
  return "ethereum"
}

/**
 * Format Ethereum chain information for detailed display
 * @param chain Ethereum chain type
 * @returns Object with formatted chain information
 */
export function getEthereumChainDisplayInfo(chain: TypeChainName) {
  return {
    name: formatEthereumChainName(chain),
    symbol: getEthereumCurrencySymbol(chain),
    shortName: getEthereumShortName(chain),
    networkType: getEthereumNetworkType(chain),
    family: getEthereumBlockchainFamily(chain)
  }
}

// ============================================================================
// ADDRESS GENERATION - Ethereum-specific functions from address-generator.ts
// ============================================================================

/**
 * Generate a mock Ethereum address for the specified chain
 * @param chain Ethereum chain type
 * @returns Mock Ethereum address string in hex format
 *
 * @example
 * ```typescript
 * generateMockEthereumAddress(staticDataChainList.ETHEREUM_MAINNET) // Returns hex address
 * generateMockEthereumAddress(staticDataChainList.ETHEREUM_TESTNET) // Returns hex address
 * ```
 */
export function generateMockEthereumAddress(chain: TypeChainName): string {
  if (!isEthereumChain(chain)) {
    throw new Error(`Invalid Ethereum chain type: ${chain}`)
  }
  return generateEthereumAddress()
}

/**
 * Validate if an address matches the Ethereum format
 * @param address Address string to validate
 * @param chain Ethereum chain type to validate against
 * @returns True if address format is valid for Ethereum
 *
 * @example
 * ```typescript
 * isValidEthereumAddressFormat("******************************************", staticDataChainList.ETHEREUM_MAINNET) // Returns true
 * isValidEthereumAddressFormat("invalid", staticDataChainList.ETHEREUM_MAINNET) // Returns false
 * ```
 */
export function isValidEthereumAddressFormat(address: string, chain: TypeChainName): boolean {
  if (!isEthereumChain(chain)) {
    return false
  }
  return validateEthereumAddress(address)
}

/**
 * Get the expected address length for Ethereum chains
 * @param chain Ethereum chain type
 * @returns Expected address length for Ethereum (fixed at 42 characters)
 */
export function getEthereumAddressLengthForChain(chain: TypeChainName): { min: number; max: number } {
  if (!isEthereumChain(chain)) {
    return { min: 0, max: 0 }
  }
  return getEthereumAddressLength()
}

/**
 * Get the address format description for Ethereum chains
 * @param chain Ethereum chain type
 * @returns Human-readable description of Ethereum address format
 */
export function getEthereumAddressFormatDescriptionForChain(chain: TypeChainName): string {
  if (!isEthereumChain(chain)) {
    return "Unknown address format"
  }
  return getEthereumAddressFormatDescription()
}
