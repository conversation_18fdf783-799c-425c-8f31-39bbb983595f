import { and, eq } from "drizzle-orm"
import { db } from "../db"
import { tableFees } from "../db/schema/fees"
import { log } from "../log"
import { calculateFeeDeduction } from "../tools/shared"

// Import types
import type { TypeChainValue } from "../db/schema/wallets"

/**
 * Fees model class for managing fee configurations
 *
 * This class provides methods for creating, retrieving, and managing fee configurations
 * in the PostgreSQL database. Each fee configuration is specific to a blockchain network
 * and defines the default fee percentage and receiver address for trading operations.
 */
export class Fees {
  /**
   * Create a new fee configuration
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier
   * @param fee Fee percentage as string (e.g., "0.3" for 0.3%)
   * @param receiver Address/PublicKey of the fee receiver
   * @returns The created fee configuration or null if creation failed
   */
  static async create(chain: TypeChainValue, chainId: number, fee: string, receiver: string) {
    try {
      // Check if fee configuration already exists for this chain and chainId
      const existingFee = await this.getByChainAndId(chain, chainId)
      if (existingFee) {
        return null // Fee configuration already exists
      }

      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      // Create new fee configuration
      await db
        .insert(tableFees)
        .values({
          chain,
          chainId,
          fee: validatedFee,
          receiver
        })
        .execute()

      // Get the created fee configuration
      return this.getByChainAndId(chain, chainId)
    } catch (error) {
      log.error(`Failed to create fee configuration: ${error}`)
      return null
    }
  }

  /**
   * Get a fee configuration by ID
   * @param id Fee configuration ID
   * @returns The fee configuration or null if not found
   */
  static async getById(id: number) {
    try {
      const fee = await db.select().from(tableFees).where(eq(tableFees.id, id)).limit(1)
      return fee[0] || null
    } catch (error) {
      log.error(`Failed to get fee configuration by ID: ${error}`)
      return null
    }
  }

  /**
   * Get fee configuration by chain and chain ID
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier
   * @returns The fee configuration or null if not found
   */
  static async getByChainAndId(chain: TypeChainValue, chainId: number) {
    try {
      const fee = await db
        .select()
        .from(tableFees)
        .where(and(eq(tableFees.chain, chain), eq(tableFees.chainId, chainId)))
        .limit(1)
      return fee[0] || null
    } catch (error) {
      log.error(`Failed to get fee configuration by chain and ID: ${error}`)
      return null
    }
  }

  /**
   * Get all fee configurations for a specific chain
   * @param chain Blockchain network
   * @returns Array of fee configurations
   */
  static async getByChain(chain: TypeChainValue) {
    try {
      return await db.select().from(tableFees).where(eq(tableFees.chain, chain))
    } catch (error) {
      log.error(`Failed to get fee configurations by chain: ${error}`)
      return []
    }
  }

  /**
   * Get all fee configurations
   * @returns Array of all fee configurations
   */
  static async getAll() {
    try {
      return await db.select().from(tableFees)
    } catch (error) {
      log.error(`Failed to get all fee configurations: ${error}`)
      return []
    }
  }

  /**
   * Update fee percentage for a configuration
   * @param id Fee configuration ID
   * @param fee New fee percentage as string (e.g., "0.3" for 0.3%)
   * @returns Updated fee configuration or null if operation failed
   */
  static async updateFee(id: number, fee: string) {
    try {
      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      await db.update(tableFees).set({ fee: validatedFee }).where(eq(tableFees.id, id)).execute()

      return this.getById(id)
    } catch (error) {
      log.error(`Failed to update fee: ${error}`)
      return null
    }
  }

  /**
   * Update receiver address for a configuration
   * @param id Fee configuration ID
   * @param receiver New receiver address/PublicKey
   * @returns Updated fee configuration or null if operation failed
   */
  static async updateReceiver(id: number, receiver: string) {
    try {
      await db.update(tableFees).set({ receiver }).where(eq(tableFees.id, id)).execute()

      return this.getById(id)
    } catch (error) {
      log.error(`Failed to update receiver: ${error}`)
      return null
    }
  }

  /**
   * Update both fee and receiver for a configuration
   * @param id Fee configuration ID
   * @param fee New fee percentage as string (e.g., "0.3" for 0.3%)
   * @param receiver New receiver address/PublicKey
   * @returns Updated fee configuration or null if operation failed
   */
  static async updateFeeAndReceiver(id: number, fee: string, receiver: string) {
    try {
      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      await db
        .update(tableFees)
        .set({
          fee: validatedFee,
          receiver
        })
        .where(eq(tableFees.id, id))
        .execute()

      return this.getById(id)
    } catch (error) {
      log.error(`Failed to update fee and receiver: ${error}`)
      return null
    }
  }

  /**
   * Delete a fee configuration
   * @param id Fee configuration ID
   * @returns True if deletion was successful, false otherwise
   */
  static async delete(id: number) {
    try {
      await db.delete(tableFees).where(eq(tableFees.id, id))
      return true
    } catch (error) {
      log.error(`Failed to delete fee configuration: ${error}`)
      return false
    }
  }

  /**
   * Get the default fee percentage for a chain
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier (optional, uses first found if not provided)
   * @returns Fee percentage as string or "0" if no configuration found
   */
  static async getDefaultFeeForChain(chain: TypeChainValue, chainId?: number) {
    try {
      let feeConfig

      if (chainId) {
        feeConfig = await this.getByChainAndId(chain, chainId)
      } else {
        const configs = await this.getByChain(chain)
        feeConfig = configs[0] || null
      }

      return feeConfig ? feeConfig.fee : "0"
    } catch (error) {
      log.error(`Failed to get default fee for chain: ${error}`)
      return "0"
    }
  }

  /**
   * Get the default receiver address for a chain
   * @param chain Blockchain network
   * @param chainId Chain-specific identifier (optional, uses first found if not provided)
   * @returns Receiver address or null if no configuration found
   */
  static async getDefaultReceiverForChain(chain: TypeChainValue, chainId?: number) {
    try {
      let feeConfig

      if (chainId) {
        feeConfig = await this.getByChainAndId(chain, chainId)
      } else {
        const configs = await this.getByChain(chain)
        feeConfig = configs[0] || null
      }

      return feeConfig ? feeConfig.receiver : null
    } catch (error) {
      log.error(`Failed to get default receiver for chain: ${error}`)
      return null
    }
  }

  /**
   * Calculate fee amount based on trade amount and chain configuration
   * Uses your specified calculation mechanism to avoid overflow/underflow issues
   * @param chain Blockchain network
   * @param tradeAmount Trade amount in the smallest unit
   * @param chainId Chain-specific identifier (optional)
   * @returns Fee amount in the same unit as trade amount
   */
  static async calculateFeeAmount(chain: TypeChainValue, tradeAmount: bigint, chainId?: number) {
    try {
      const feePercentage = await this.getDefaultFeeForChain(chain, chainId)

      if (feePercentage === "0" || parseFloat(feePercentage) === 0) {
        return BigInt(0)
      }

      // Use the improved fee calculation to avoid overflow/underflow
      const { feeAmount } = calculateFeeDeduction(tradeAmount, feePercentage)
      return feeAmount
    } catch (error) {
      log.error(`Failed to calculate fee amount: ${error}`)
      return BigInt(0)
    }
  }

  /**
   * Calculate amount after fee deduction based on trade amount and chain configuration
   * Uses your specified calculation mechanism to avoid overflow/underflow issues
   * @param chain Blockchain network
   * @param tradeAmount Trade amount in the smallest unit
   * @param chainId Chain-specific identifier (optional)
   * @returns Object with amountAfterFee and feeAmount
   */
  static async calculateAmountAfterFee(chain: TypeChainValue, tradeAmount: bigint, chainId?: number) {
    try {
      const feePercentage = await this.getDefaultFeeForChain(chain, chainId)

      if (feePercentage === "0" || parseFloat(feePercentage) === 0) {
        return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
      }

      // Use the improved fee calculation to avoid overflow/underflow
      return calculateFeeDeduction(tradeAmount, feePercentage)
    } catch (error) {
      log.error(`Failed to calculate amount after fee: ${error}`)
      return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
    }
  }
}
