import { eq } from "drizzle-orm"
import { db } from "../db"
import { UserRole, tableUsers } from "../db/schema/users"
import { log } from "../log"
import { calculateFeeDeduction } from "../tools/shared"

// Import types
import type { TypeUserStats } from "../db/schema/users"

/**
 * User model class for managing Telegram bot users
 *
 * This class provides methods for creating, retrieving, and updating user records
 * in the PostgreSQL database. Each user represents a Telegram user who interacts
 * with the trading bot.
 */

export class User {
  /**
   * Get a user by Telegram username
   * @param username Telegram username
   * @returns The user or null if not found
   */
  static async getByUsername(username: string) {
    try {
      const user = await db.select().from(tableUsers).where(eq(tableUsers.username, username)).limit(1)
      return user[0] || null
    } catch (error) {
      log.error(`Failed to get user by username: ${error}`)
      return null
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns The user or null if not found
   */
  static async getById(id: number) {
    try {
      const user = await db.select().from(tableUsers).where(eq(tableUsers.id, id)).limit(1)
      return user[0] || null
    } catch (error) {
      log.error(`Failed to get user by ID: ${error}`)
      return null
    }
  }

  /**
   * Create a new user
   * @param telegramId Telegram user ID
   * @param username Telegram username (without @ symbol)
   * @param fullname User's full name
   * @returns The created user or null if creation failed
   */
  static async create(telegramId: number, username: string, fullname: string) {
    try {
      // Check if user already exists
      const existingUser = await this.getById(telegramId)
      if (existingUser) {
        return existingUser
      }

      // Create new user with default stats
      await db
        .insert(tableUsers)
        .values({
          id: telegramId,
          username: username || "",
          fullname: fullname || "",
          role: UserRole.USER,
          isActive: true,
          fee: "0",
          stats: {
            language: "en",
            networks: {}
          }
        })
        .execute()

      // Get the created user
      return this.getById(telegramId)
    } catch (error) {
      log.error(`Failed to create user: ${error}`)
      return null
    }
  }

  /**
   * Get or create a user
   * @param telegramId Telegram user ID
   * @param username Telegram username
   * @param fullname User's full name
   * @returns The user or null if operation failed
   */
  static async getOrCreate(telegramId: number, username: string, fullname: string) {
    const user = await this.getById(telegramId)
    if (user) {
      return user
    }
    return this.create(telegramId, username, fullname)
  }

  /**
   * Update user's active status
   * @param userId User ID
   * @param isActive Whether the user should be active
   * @returns Updated user or null if operation failed
   */
  static async updateActiveStatus(userId: number, isActive: boolean) {
    try {
      await db.update(tableUsers).set({ isActive }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed to update user active status: ${error}`)
      return null
    }
  }

  /**
   * Update user's fee percentage
   * @param userId User ID
   * @param fee Fee percentage as string (e.g., "0.3" for 0.3%)
   * @returns Updated user or null if operation failed
   */
  static async updateFee(userId: number, fee: string) {
    try {
      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      await db.update(tableUsers).set({ fee: validatedFee }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed to update user fee: ${error}`)
      return null
    }
  }

  /**
   * Calculate user-specific fee amount using your specified calculation mechanism
   * Uses your specified approach to avoid overflow/underflow issues
   * @param userId User ID
   * @param tradeAmount Trade amount in the smallest unit
   * @returns Object with amountAfterFee and feeAmount
   */
  static async calculateUserFee(userId: number, tradeAmount: bigint) {
    try {
      const user = await this.getById(userId)

      if (!user || user.fee === "0" || parseFloat(user.fee) === 0) {
        return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
      }

      // Use the improved fee calculation to avoid overflow/underflow
      return calculateFeeDeduction(tradeAmount, user.fee)
    } catch (error) {
      log.error(`Failed to calculate user fee: ${error}`)
      return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
    }
  }

  /**
   * Update user's language preference
   * @param userId User ID
   * @param language Language code (e.g., "en", "es", "fr")
   * @returns Updated user or null if operation failed
   */
  static async updateLanguage(userId: number, language: string) {
    try {
      const user = await this.getById(userId)
      if (!user) {
        return null
      }

      // Update the language in the stats object
      const updatedStats = {
        ...user.stats,
        language
      }

      await db.update(tableUsers).set({ stats: updatedStats }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed to update user language: ${error}`)
      return null
    }
  }

  /**
   * Update user's trading statistics for a specific network
   * @param userId User ID
   * @param network Blockchain network
   * @param networkStats Network-specific statistics to update
   * @returns Updated user or null if operation failed
   */
  static async updateNetworkStats(userId: number, network: string, networkStats: Partial<TypeUserStats["networks"][string]>) {
    try {
      const user = await this.getById(userId)
      if (!user) {
        return null
      }

      // Get current stats
      const stats = { ...user.stats }

      // Initialize network stats if they don't exist
      if (!stats.networks[network]) {
        stats.networks[network] = {
          totalTrades: 0,
          successTrades: 0,
          failedTrades: 0,
          totalProfit: "0"
        }
      }

      // Update network stats
      stats.networks[network] = {
        ...stats.networks[network],
        ...networkStats
      }

      // Update in database
      await db.update(tableUsers).set({ stats }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed to update user stats: ${error}`)
      return null
    }
  }

  /**
   * Get all active users
   * @returns Array of active users
   */
  static async getAllActive() {
    try {
      return await db.select().from(tableUsers).where(eq(tableUsers.isActive, true))
    } catch (error) {
      log.error(`Failed to get active users: ${error}`)
      return []
    }
  }

  /**
   * Get users by role
   * @param role User role to filter by
   * @returns Array of users with the specified role
   */
  static async getByRole(role: typeof UserRole.USER | typeof UserRole.ADMIN) {
    try {
      return await db.select().from(tableUsers).where(eq(tableUsers.role, role))
    } catch (error) {
      log.error(`Failed to get users by role: ${error}`)
      return []
    }
  }

  /**
   * Update user's role (admin only operation)
   * @param userId User ID
   * @param role New role for the user
   * @returns Updated user or null if operation failed
   */
  static async updateRole(userId: number, role: typeof UserRole.USER | typeof UserRole.ADMIN) {
    try {
      await db.update(tableUsers).set({ role }).where(eq(tableUsers.id, userId)).execute()

      return this.getById(userId)
    } catch (error) {
      log.error(`Failed to update user role: ${error}`)
      return null
    }
  }
}
