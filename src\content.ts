import { watch } from "chokidar"
import { basename, join } from "node:path"
import { readdirSync, readFileSync } from "node:fs"
import { readFile, writeFile } from "node:fs/promises"
import { render } from "micromustache"

export class Content {
  public stateContent = new Map<string, string>()
  constructor(public filterExtensionFile: string = `md`, public pathContent: string = join(process.cwd(), `content`)) {
    // watch for changes in the content folder (example only md files) and store to state mapping (stateContent) on change
    watch(this.pathContent, {
      ignored: (path, stats: any) => stats?.isFile() && !path.endsWith(`.${this.filterExtensionFile}`), // only watch {fileExt} files
      persistent: true
    }).on(`change`, async (path) => {
      const fullFileName = basename(path)
      const pathFile = join(this.pathContent, fullFileName)
      const content = await readFile(pathFile, { encoding: `utf8` })
      const escapedContent = this.escapeMarkdownV2(content)
      this.stateContent.set(fullFileName, escapedContent)
    })

    // read all files and store to state mapping (stateContent) on start
    const arrFn = readdirSync(this.pathContent, { encoding: `utf8` })
    for (let index = 0; index < arrFn.length; index++) {
      const fullFileName = arrFn[index]
      if (fullFileName && fullFileName.endsWith(`.${this.filterExtensionFile}`) === true) {
        const pathFile = join(this.pathContent, fullFileName)
        const content = readFileSync(pathFile, { encoding: `utf8` })
        const escapedContent = this.escapeMarkdownV2(content)
        this.stateContent.set(fullFileName, escapedContent)
      }
    }
  }

  /**
   * Get content from state mapping (stateContent)
   * @param fileName The file name to get content from
   * @param data Optional data to replace variables in the content
   * @returns The content with variables replaced
   */
  public get(fileName: string, data?: Record<string, any>) {
    const content = this.stateContent.get(`${fileName}.${this.filterExtensionFile}`) || ``
    if (fileName == ``)
    if (data) {
      return render(content, data)
    }
    return content
  }

  /**
   * Set content in state mapping (stateContent)
   * @param fileName The file name to set content for
   * @param content The content to set
   */
  public set(fileName: string, content: string) {
    const escapedContent = this.escapeMarkdownV2(content)
    this.stateContent.set(`${fileName}.${this.filterExtensionFile}`, escapedContent)
  }

  /**
   * Write content to file and set in state mapping (stateContent)
   * @param fileName The file name to write content to
   * @param content The content to write
   * @returns A promise that resolves when the file is written
   */
  public write(fileName: string, content: string) {
    this.set(fileName, content)
    const fullFileName = `${fileName}.${this.filterExtensionFile}`
    const pathFile = join(this.pathContent, fullFileName)
    return writeFile(pathFile, content, { encoding: `utf8` })
  }

  /**
   * Escapes special characters for Telegram's MarkdownV2 format
   * @param text The text to escape
   * @returns The escaped text
   */
  private escapeMarkdownV2(text: string): string {
    if (!text || typeof text !== "string") {
      return ""
    }

    // Complex regex patterns for different types of escaping
    const patterns = [
      // Escape backslashes first (must be done before other escapes)
      { regex: /\\/g, replacement: "\\\\" },

      // Escape underscores (but preserve those already escaped or in code blocks)
      { regex: /(?<!\\)_/g, replacement: "\\_" },

      // Escape asterisks (but preserve those already escaped or in code blocks)
      { regex: /(?<!\\)\*/g, replacement: "\\*" },

      // Escape square brackets (but preserve those already escaped)
      { regex: /(?<!\\)\[/g, replacement: "\\[" },
      { regex: /(?<!\\)\]/g, replacement: "\\]" },

      // Escape parentheses (but preserve those already escaped)
      { regex: /(?<!\\)\(/g, replacement: "\\(" },
      { regex: /(?<!\\)\)/g, replacement: "\\)" },

      // Escape tilde (but preserve those already escaped)
      { regex: /(?<!\\)~/g, replacement: "\\~" },

      // Escape backticks (but preserve those already escaped or in code blocks)
      { regex: /(?<!\\)`/g, replacement: "\\`" },

      // Escape greater than symbol (but preserve those already escaped)
      { regex: /(?<!\\)>/g, replacement: "\\>" },

      // Escape hash symbol (but preserve those already escaped)
      { regex: /(?<!\\)#/g, replacement: "\\#" },

      // Escape plus symbol (but preserve those already escaped)
      { regex: /(?<!\\)\+/g, replacement: "\\+" },

      // Escape minus/hyphen symbol (but preserve those already escaped)
      { regex: /(?<!\\)-/g, replacement: "\\-" },

      // Escape equals symbol (but preserve those already escaped)
      { regex: /(?<!\\)=/g, replacement: "\\=" },

      // Escape pipe symbol (but preserve those already escaped)
      { regex: /(?<!\\)\|/g, replacement: "\\|" },

      // Escape curly braces (but preserve those already escaped)
      { regex: /(?<!\\)\{/g, replacement: "\\{" },
      { regex: /(?<!\\)\}/g, replacement: "\\}" },

      // Escape dot symbol (but preserve those already escaped)
      { regex: /(?<!\\)\./g, replacement: "\\." },

      // Escape exclamation mark (but preserve those already escaped)
      { regex: /(?<!\\)!/g, replacement: "\\!" }
    ]

    // Apply all escape patterns sequentially
    let escapedText = text
    for (const pattern of patterns) {
      escapedText = escapedText.replace(pattern.regex, pattern.replacement)
    }

    // Additional complex patterns for special cases

    // Handle code blocks - preserve content inside triple backticks
    escapedText = escapedText.replace(/```([\s\S]*?)```/g, (_, content) => {
      // Remove escaping from content inside code blocks
      const unescapedContent = content.replace(/\\([_*\[\]()~`>#+=|\{\}.!-])/g, "$1")
      return "```" + unescapedContent + "```"
    })

    // Handle inline code - preserve content inside single backticks
    escapedText = escapedText.replace(/`([^`]+)`/g, (_, content) => {
      // Remove escaping from content inside inline code
      const unescapedContent = content.replace(/\\([_*\[\]()~`>#+=|\{\}.!-])/g, "$1")
      return "`" + unescapedContent + "`"
    })

    // Handle pre-formatted text blocks
    escapedText = escapedText.replace(/```([a-zA-Z]*)\n([\s\S]*?)```/g, (_, language, content) => {
      // Remove escaping from content inside pre-formatted blocks
      const unescapedContent = content.replace(/\\([_*\[\]()~`>#+=|\{\}.!-])/g, "$1")
      return "```" + language + "\n" + unescapedContent + "```"
    })

    // Handle URLs - don't escape certain characters in URLs
    escapedText = escapedText.replace(/(https?:\/\/[^\s\]]+)/g, (match) => {
      // Restore some characters that shouldn't be escaped in URLs
      return match.replace(/\\([./#?&=])/g, "$1")
    })

    // Handle email addresses - don't escape @ and dots in emails
    escapedText = escapedText.replace(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g, (match) => {
      // Restore dots and other characters in email addresses
      return match.replace(/\\([.@])/g, "$1")
    })

    return escapedText
  }
}
