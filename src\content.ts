import { watch } from "chokidar"
import { basename, join } from "node:path"
import { readdirSync, readFileSync } from "node:fs"
import { readFile, writeFile } from "node:fs/promises"
import { render } from "micromustache"

export const content = (filterExtensionFile: string = `html`, pathContent: string = join(process.cwd(), `content`)) => {
  // watch for changes in the content folder and store to state mapping (stateContent) on change
  let stateContent = new Map<string, string>()
  watch(pathContent, {
    ignored: (path, stats: any) => stats?.isFile() && !path.endsWith(`.${filterExtensionFile}`), // only watch {fileExt} files
    persistent: true
  }).on(`change`, async (path) => {
    const fullFileName = basename(path)
    const pathFile = join(pathContent, fullFileName)
    const content = await readFile(pathFile, { encoding: `utf8` })
    stateContent.set(fullFileName, content)
  })

  // read all files and store to state mapping (stateContent) on start
  const arrFn = readdirSync(pathContent, { encoding: `utf8` })
  for (let index = 0; index < arrFn.length; index++) {
    const fullFileName = arrFn[index]
    if (fullFileName && fullFileName.endsWith(`.${filterExtensionFile}`) === true) {
      const pathFile = join(pathContent, fullFileName)
      const content = readFileSync(pathFile, { encoding: `utf8` })
      stateContent.set(fullFileName, content)
    }
  }

  /**
   * Get content from state mapping (stateContent)
   * @param fileName The file name to get content from
   * @param data Optional data to replace variables in the content
   * @returns The content with variables replaced
   */
  const get = (fileName: string, data?: Record<string, any>) => {
    const content = stateContent.get(`${fileName}.${filterExtensionFile}`) || ``
    if (data) {
      return render(content, data)
    }
    return content
  }

  /**
   * Set content in state mapping (stateContent)
   * @param fileName The file name to set content for
   * @param content The content to set
   */
  const set = (fileName: string, content: string) => {
    stateContent.set(`${fileName}.${filterExtensionFile}`, content)
  }

  /**
   * Write content to file and set in state mapping (stateContent)
   * @param fileName The file name to write content to
   * @param content The content to write
   * @returns A promise that resolves when the file is written
   */
  const write = (fileName: string, content: string) => {
    set(fileName, content)
    const fullFileName = `${fileName}.${filterExtensionFile}`
    const pathFile = join(pathContent, fullFileName)
    return writeFile(pathFile, content, { encoding: `utf8` })
  }

  return { get, set, write }
}
