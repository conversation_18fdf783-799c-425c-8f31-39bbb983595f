/**
 * Wallets table schema
 *
 * This file defines the wallets table schema.
 * The primary key is an auto-incrementing ID.
 * The owner is a foreign key to the users table.
 * Wallet data is stored in JSON fields for flexibility.
 */
import { sql } from "drizzle-orm"
import { bigint, integer, pgEnum, pgTable, serial, timestamp, varchar } from "drizzle-orm/pg-core"
import { tableUsers } from "./users"
import { dataChainName } from "../../data"

export const chainTypeEnum = pgEnum("chain_type", dataChainName as [string, ...string[]])
export const createdByEnum = pgEnum("created_by", ["system", "import"])
export const tableWallets = pgTable("wallets", {
  id: serial().primaryKey(), // Wallet id
  owner: integer()
    .notNull()
    .references(() => tableUsers.id),
  name: varchar({ length: 32 }).notNull(), // Name of the wallet
  chain: chainTypeEnum().notNull(), // Chain type
  chainId: varchar({ length: 10 }).notNull(), // Chain-specific ID
  address: varchar({ length: 110 }).notNull(), // Address of the wallet
  balance: bigint({ mode: "bigint" }).notNull(), // Balance of the wallet
  seed: varchar({ length: 512 }).notNull(), // Seed wallet
  createdBy: createdByEnum().notNull().default("system"), // Indicates how the wallet was created
  updatedAt: timestamp()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => new Date()),
  createdAt: timestamp().default(sql`CURRENT_TIMESTAMP`)
})
